// worker.js - <PERSON>flare Workers API Code

// Rate limiting storage
const rateLimitStore = new Map();

/**
 * Google Drive backup functionality class
 */
class GoogleDriveBackup {
  constructor(credentials, folderId) {
    this.credentials = JSON.parse(credentials);
    this.folderId = folderId;
    this.accessToken = null;
    this.tokenExpiry = null;
  }

  /**
   * Get Google Drive API access token
   */
async getAccessToken() {
  // Check if existing token is still valid
  if (this.accessToken && this.tokenExpiry && Date.now() < this.tokenExpiry) {
    console.log('Using cached access token');
    return this.accessToken;
  }

  console.log('Generating new access token...');

  // Create JWT assertion
  const header = {
    alg: 'RS256',
    typ: 'JWT'
  };

  const now = Math.floor(Date.now() / 1000);
  const payload = {
    iss: this.credentials.client_email,
    scope: 'https://www.googleapis.com/auth/drive.file', // Use more specific permission scope
    aud: 'https://oauth2.googleapis.com/token',
    exp: now + 3600,
    iat: now
  };

  console.log('JWT payload:', {
    iss: payload.iss,
    scope: payload.scope,
    exp: new Date(payload.exp * 1000).toISOString()
  });

  // Sign JWT using Web Crypto API
  const jwt = await this.signJWT(header, payload, this.credentials.private_key);

  // Request access token
  const response = await fetch('https://oauth2.googleapis.com/token', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    body: new URLSearchParams({
      grant_type: 'urn:ietf:params:oauth:grant-type:jwt-bearer',
      assertion: jwt
    })
  });

  console.log('Token request response:', response.status);

  if (!response.ok) {
    const errorText = await response.text();
    console.error('Failed to get access token:', {
      status: response.status,
      statusText: response.statusText,
      error: errorText
    });
    throw new Error(`Failed to get access token: ${response.statusText}`);
  }

  const tokenData = await response.json();
  this.accessToken = tokenData.access_token;
  this.tokenExpiry = Date.now() + (tokenData.expires_in * 1000) - 60000; // Expire 1 minute early

  console.log('New access token obtained, expires in:', tokenData.expires_in, 'seconds');

  return this.accessToken;
}

  /**
   * Sign JWT
   */
  async signJWT(header, payload, privateKey) {
    const encoder = new TextEncoder();

    // Base64URL encoding
    const base64UrlEncode = (data) => {
      return btoa(String.fromCharCode(...new Uint8Array(data)))
        .replace(/\+/g, '-')
        .replace(/\//g, '_')
        .replace(/=/g, '');
    };

    const encodedHeader = base64UrlEncode(encoder.encode(JSON.stringify(header)));
    const encodedPayload = base64UrlEncode(encoder.encode(JSON.stringify(payload)));
    const message = `${encodedHeader}.${encodedPayload}`;

    // Import private key
    const keyData = privateKey
      .replace(/-----BEGIN PRIVATE KEY-----/, '')
      .replace(/-----END PRIVATE KEY-----/, '')
      .replace(/\s/g, '');

    const binaryKey = Uint8Array.from(atob(keyData), c => c.charCodeAt(0));

    const cryptoKey = await crypto.subtle.importKey(
      'pkcs8',
      binaryKey,
      {
        name: 'RSASSA-PKCS1-v1_5',
        hash: 'SHA-256'
      },
      false,
      ['sign']
    );

    // Sign
    const signature = await crypto.subtle.sign(
      'RSASSA-PKCS1-v1_5',
      cryptoKey,
      encoder.encode(message)
    );

    const encodedSignature = base64UrlEncode(signature);
    return `${message}.${encodedSignature}`;
  }

  /**
   * Backup database to Google Drive
   */
  async backupDatabase(db) {
    try {
      console.log('Starting database backup to Google Drive...');

      // Export database data
      const backupData = await this.exportDatabaseData(db);

      // Create backup filename
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const fileName = `database-backup-${timestamp}.json`;

      // Upload to Google Drive
      const fileId = await this.uploadFile(fileName, JSON.stringify(backupData, null, 2));

      console.log(`Database backup successful, file ID: ${fileId}`);
      return { success: true, fileId, fileName };

    } catch (error) {
      console.error('Database backup failed:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Export database data
   */
  async exportDatabaseData(db) {
    const backupData = {
      timestamp: new Date().toISOString(),
      version: '1.0',
      data: {}
    };

    // Export users table
    const users = await db.prepare('SELECT * FROM users').all();
    backupData.data.users = users.results || [];

    // Export notes table
    const notes = await db.prepare('SELECT * FROM notes').all();
    backupData.data.notes = notes.results || [];

    // Add statistics
    backupData.stats = {
      userCount: backupData.data.users.length,
      noteCount: backupData.data.notes.length,
      backupSize: JSON.stringify(backupData).length
    };

    return backupData;
  }

/**
 * Upload file to Google Drive - Fix 403 permission issues
 */
async uploadFile(fileName, content) {
  const accessToken = await this.getAccessToken();

  // First, verify the folder exists and is accessible
  await this.verifyFolderAccess(accessToken);

  console.log('Starting Google Drive upload:', {
    fileName,
    folderId: this.folderId,
    contentLength: content.length
  });

  // Method 1: Use one-step multipart upload (recommended)
  try {
    const result = await this.uploadFileOneStep(fileName, content, accessToken);
    console.log('One-step upload successful:', result.id);
    return result.id;
  } catch (error) {
    console.warn('One-step upload failed, trying resumable upload:', error.message);
    
    // Method 2: Use resumable upload
    try {
      const result = await this.uploadFileResumable(fileName, content, accessToken);
      console.log('Resumable upload successful:', result.id);
      return result.id;
    } catch (resumableError) {
      console.error('All upload methods failed');
      throw resumableError;
    }
  }
}

/**
 * One-step multipart upload (avoid permission issues)
 */
async uploadFileOneStep(fileName, content, accessToken) {
  const boundary = `----formdata-cloudflare-${Date.now()}`;
  
  const metadata = {
    name: fileName,
    parents: [this.folderId]
    // Don't set mimeType, let Google Drive auto-detect
  };

  // Build correct multipart/related request body
  const delimiter = `\r\n--${boundary}\r\n`;
  const closeDelim = `\r\n--${boundary}--`;

  let body = delimiter + 
    'Content-Type: application/json\r\n\r\n' +
    JSON.stringify(metadata) +
    delimiter +
    'Content-Type: application/json\r\n' +
    'Content-Transfer-Encoding: binary\r\n\r\n' +
    content +
    closeDelim;

  console.log('Upload request details:', {
    boundary,
    bodyLength: body.length,
    contentLength: content.length
  });

  const uploadUrl = 'https://www.googleapis.com/upload/drive/v3/files?uploadType=multipart&fields=id,name,size,mimeType';

  const response = await fetch(uploadUrl, {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${accessToken}`,
      'Content-Type': `multipart/related; boundary=${boundary}`,
      'Content-Length': body.length.toString()
    },
    body: body
  });

  console.log('Upload response status:', response.status);

  if (!response.ok) {
    const errorText = await response.text();
    console.error('Upload failed:', {
      status: response.status,
      statusText: response.statusText,
      error: errorText,
      headers: Object.fromEntries(response.headers.entries())
    });

    // Parse error details
    try {
      const errorJson = JSON.parse(errorText);
      console.error('Parsed error:', errorJson);
      throw new Error(`Upload failed: ${errorJson.error?.message || errorText}`);
    } catch (parseError) {
      throw new Error(`Upload failed: ${response.status} ${response.statusText} - ${errorText}`);
    }
  }

  const result = await response.json();
  console.log('Upload successful:', result);
  return result;
}

/**
 * Resumable upload method (handle permission-sensitive scenarios)
 */
async uploadFileResumable(fileName, content, accessToken) {
  console.log('Starting resumable upload...');

  // Step 1: Initialize resumable upload
  const metadata = {
    name: fileName,
    parents: [this.folderId]
  };

  const initResponse = await fetch('https://www.googleapis.com/upload/drive/v3/files?uploadType=resumable&fields=id,name,size', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${accessToken}`,
      'Content-Type': 'application/json; charset=UTF-8',
      'X-Upload-Content-Type': 'application/json',
      'X-Upload-Content-Length': content.length.toString()
    },
    body: JSON.stringify(metadata)
  });

  console.log('Resumable init response:', initResponse.status);

  if (!initResponse.ok) {
    const errorText = await initResponse.text();
    console.error('Resumable upload init failed:', {
      status: initResponse.status,
      statusText: initResponse.statusText,
      error: errorText
    });
    throw new Error(`Resumable init failed: ${initResponse.status} ${initResponse.statusText}`);
  }

  const uploadUrl = initResponse.headers.get('Location');
  if (!uploadUrl) {
    throw new Error('No upload URL returned from resumable init');
  }

  console.log('Got upload URL, uploading content...');

  // Step 2: Upload file content
  const uploadResponse = await fetch(uploadUrl, {
    method: 'PUT',
    headers: {
      'Content-Length': content.length.toString()
    },
    body: content
  });

  console.log('Content upload response:', uploadResponse.status);

  if (!uploadResponse.ok) {
    const errorText = await uploadResponse.text();
    console.error('Resumable content upload failed:', {
      status: uploadResponse.status,
      statusText: uploadResponse.statusText,
      error: errorText
    });
    throw new Error(`Content upload failed: ${uploadResponse.status} ${uploadResponse.statusText}`);
  }

  const result = await uploadResponse.json();
  console.log('Resumable upload successful:', result);
  return result;
}

/**
 * Enhanced folder access verification (check specific permissions)
 */
async verifyFolderAccess(accessToken) {
  console.log(`Verifying access to Google Drive folder: ${this.folderId}`);

  // Get detailed folder information including permissions
  const response = await fetch(`https://www.googleapis.com/drive/v3/files/${this.folderId}?fields=id,name,mimeType,capabilities,permissions`, {
    method: 'GET',
    headers: {
      'Authorization': `Bearer ${accessToken}`
    }
  });

  if (!response.ok) {
    const errorText = await response.text();
    console.error('Folder verification failed:', {
      status: response.status,
      statusText: response.statusText,
      error: errorText,
      folderId: this.folderId
    });

    if (response.status === 404) {
      throw new Error('Google Drive folder not found');
    } else if (response.status === 403) {
      throw new Error('Access denied to Google Drive folder');
    } else {
      throw new Error('Cannot access Google Drive folder');
    }
  }

  const folderInfo = await response.json();
  console.log('Folder verification successful:', {
    name: folderInfo.name,
    id: folderInfo.id,
    mimeType: folderInfo.mimeType,
    capabilities: folderInfo.capabilities
  });

  // Check critical permissions
  if (folderInfo.capabilities) {
    const canAddChildren = folderInfo.capabilities.canAddChildren;
    const canEdit = folderInfo.capabilities.canEdit;
    
    console.log('Folder permissions:', {
      canAddChildren,
      canEdit
    });

    if (!canAddChildren) {
      console.warn('Warning: Cannot add children to this folder. Upload may fail.');
    }
    if (!canEdit) {
      console.warn('Warning: Cannot edit this folder. Upload may fail.');
    }
  }

  return folderInfo;
}

/**
 * Permission issue diagnostic tool
 */
async diagnosePermissionIssue(accessToken) {
  console.log('=== Diagnosing Google Drive Permission Issues ===');

  try {
    // 1. Check service account information
    const tokenInfo = await fetch(`https://www.googleapis.com/oauth2/v1/tokeninfo?access_token=${accessToken}`);
    if (tokenInfo.ok) {
      const info = await tokenInfo.json();
      console.log('Token info:', {
        scope: info.scope,
        audience: info.audience,
        expires_in: info.expires_in
      });
    }

    // 2. Check Drive API quota and limits
    const aboutResponse = await fetch('https://www.googleapis.com/drive/v3/about?fields=user,storageQuota', {
      headers: { 'Authorization': `Bearer ${accessToken}` }
    });
    
    if (aboutResponse.ok) {
      const about = await aboutResponse.json();
      console.log('Drive account info:', about);
    }

    // 3. Try to list folder contents
    const listResponse = await fetch(`https://www.googleapis.com/drive/v3/files?q='${this.folderId}'+in+parents&fields=files(id,name)`, {
      headers: { 'Authorization': `Bearer ${accessToken}` }
    });

    if (listResponse.ok) {
      const files = await listResponse.json();
      console.log(`Folder contains ${files.files?.length || 0} files`);
    } else {
      console.error('Cannot list folder contents:', listResponse.status);
    }

  } catch (error) {
    console.error('Diagnosis failed:', error.message);
  }

  console.log('=== End Diagnosis ===');
}

  /**
   * Clean up old backup files (keep recent specified count)
   */
  async cleanupOldBackups(retentionCount = 30) {
    try {
      const accessToken = await this.getAccessToken();

      // Query all backup files in the backup folder
      const query = `'${this.folderId}' in parents and name contains 'database-backup-'`;
      const response = await fetch(
        `https://www.googleapis.com/drive/v3/files?q=${encodeURIComponent(query)}&orderBy=createdTime desc&fields=files(id,name,createdTime)`,
        {
          headers: {
            'Authorization': `Bearer ${accessToken}`
          }
        }
      );

      if (!response.ok) {
        throw new Error(`Failed to list files: ${response.statusText}`);
      }

      const data = await response.json();
      const files = data.files || [];

      // Keep the most recent specified number of files, delete the rest
      const filesToDelete = files.slice(retentionCount);

      for (const file of filesToDelete) {
        await this.deleteFile(file.id);
        console.log(`Deleted old backup file: ${file.name}`);
      }

      return { success: true, deletedCount: filesToDelete.length };

    } catch (error) {
      console.error('Failed to clean up old backups:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Delete file
   */
  async deleteFile(fileId) {
    const accessToken = await this.getAccessToken();

    const response = await fetch(`https://www.googleapis.com/drive/v3/files/${fileId}`, {
      method: 'DELETE',
      headers: {
        'Authorization': `Bearer ${accessToken}`
      }
    });

    if (!response.ok) {
      throw new Error(`Failed to delete file: ${response.statusText}`);
    }
  }
}

/**
 * Check if user is admin
 * @param {number} userId - User ID
 * @param {Object} env - Environment variables
 * @returns {Promise<boolean>} Whether user is admin
 */
async function isAdminUser(userId, env) {
  if (!env.ADMIN_EMAIL) {
    console.error('ADMIN_EMAIL environment variable is not set');
    return false;
  }

  try {
    const user = await env.DB.prepare('SELECT email FROM users WHERE id = ?').bind(userId).first();
    console.log('Admin check - User ID:', userId, 'User email:', user?.email, 'Admin email:', env.ADMIN_EMAIL);
    return user && user.email === env.ADMIN_EMAIL;
  } catch (error) {
    console.error('Error checking admin status:', error);
    return false;
  }
}

/**
 * Check if user is disabled
 * @param {number} userId - User ID
 * @param {Object} env - Environment variables
 * @throws {Error} If user is disabled
 */
async function checkUserStatus(userId, env) {
  const user = await env.DB.prepare('SELECT is_disabled FROM users WHERE id = ?').bind(userId).first();

  if (user && user.is_disabled === 1) {
    throw new Error('Account has been disabled by administrator');
  }
}

/**
 * Strictly verify admin permissions
 * @param {string} authHeader - Authorization header
 * @param {Object} env - Environment variables
 * @returns {Promise<number>} Admin user ID
 * @throws {Error} If not admin
 */
async function verifyAdminPermission(authHeader, env) {
  // Verify JWT token
  const userId = await verifyJWT(authHeader, env.JWT_SECRET);

  // Check user status
  await checkUserStatus(userId, env);

  // Verify admin permissions
  const isAdmin = await isAdminUser(userId, env);
  if (!isAdmin) {
    throw new Error('Administrator privileges required');
  }

  return userId;
}

/**
 * Perform database backup
 */
async function performDatabaseBackup(env) {
  console.log('Starting backup process...');

  // Check if Google Drive backup is enabled
  if (!env.GOOGLE_DRIVE_ENABLED || env.GOOGLE_DRIVE_ENABLED !== 'true') {
    console.log('Google Drive backup not enabled');
    return { success: false, message: 'Backup not enabled' };
  }

  // Check required environment variables with detailed logging
  if (!env.GOOGLE_DRIVE_CREDENTIALS) {
    console.error('GOOGLE_DRIVE_CREDENTIALS environment variable is not set');
    return { success: false, error: 'Backup configuration error' };
  }

  if (!env.GOOGLE_DRIVE_FOLDER_ID) {
    console.error('GOOGLE_DRIVE_FOLDER_ID environment variable is not set');
    return { success: false, error: 'Backup configuration error' };
  }

  if (!env.DB) {
    console.error('DB environment variable is not set');
    return { success: false, error: 'Database not available' };
  }

  // Log configuration (without sensitive data)
  console.log('Backup configuration:', {
    enabled: env.GOOGLE_DRIVE_ENABLED,
    hasFolderId: !!env.GOOGLE_DRIVE_FOLDER_ID,
    hasCredentials: !!env.GOOGLE_DRIVE_CREDENTIALS,
    retentionCount: parseInt(env.BACKUP_RETENTION_COUNT) || 30
  });

  try {
    // Validate credentials format
    let credentials;
    try {
      credentials = JSON.parse(env.GOOGLE_DRIVE_CREDENTIALS);
      if (!credentials.client_email || !credentials.private_key) {
        throw new Error('Invalid credentials format');
      }
    } catch (parseError) {
      console.error('Failed to parse Google Drive credentials:', parseError.message);
      return { success: false, error: 'Invalid backup credentials format' };
    }

    // Create backup instance
    const backup = new GoogleDriveBackup(
      env.GOOGLE_DRIVE_CREDENTIALS,
      env.GOOGLE_DRIVE_FOLDER_ID
    );

    // Execute backup
    console.log('Executing database backup...');
    const backupResult = await backup.backupDatabase(env.DB);

    if (backupResult.success) {
      console.log('Backup successful, cleaning up old backups...');
      // Clean up old backups
      const retentionCount = parseInt(env.BACKUP_RETENTION_COUNT) || 30;
      try {
        await backup.cleanupOldBackups(retentionCount);
        console.log('Cleanup completed successfully');
      } catch (cleanupError) {
        console.warn('Backup successful but cleanup failed:', cleanupError.message);
        // Don't fail the entire backup for cleanup errors
      }
    }

    return backupResult;

  } catch (error) {
    console.error('Error occurred during backup process:', error);
    return { success: false, error: error.message };
  }
}

// Security: Risk-based timing protection configuration
const TIMING_PROTECTION_CONFIG = {
  HIGH_RISK: { baseDelay: 100, randomRange: 30 },    // 100ms±30ms for critical operations
  MEDIUM_RISK: { baseDelay: 50, randomRange: 20 },   // 50ms±20ms for sensitive operations
  LOW_RISK: { baseDelay: 20, randomRange: 10 },      // 20ms±10ms for error cases
  NONE: { baseDelay: 0, randomRange: 0 }             // No protection for non-sensitive operations
};

/**
 * Unified timing protection function with risk-based configuration
 * @param {number} startTime - Operation start timestamp
 * @param {string} riskLevel - Risk level: HIGH_RISK, MEDIUM_RISK, LOW_RISK, or NONE
 */
async function applyTimingProtection(startTime, riskLevel = 'NONE') {
  const config = TIMING_PROTECTION_CONFIG[riskLevel];
  if (!config || config.baseDelay === 0) {
    return; // No timing protection needed
  }

  const elapsed = Date.now() - startTime;
  const randomOffset = (Math.random() - 0.5) * 2 * config.randomRange;
  const targetDelay = Math.max(0, config.baseDelay + randomOffset - elapsed);

  if (targetDelay > 0) {
    await new Promise(resolve => setTimeout(resolve, targetDelay));
  }
}

// Security: Secure hash function
async function sha256(message) {
  const msgBuffer = new TextEncoder().encode(message);
  const hashBuffer = await crypto.subtle.digest('SHA-256', msgBuffer);
  const hashArray = Array.from(new Uint8Array(hashBuffer));
  return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
}

// Security: Rate limiting function
function checkRateLimit(clientIP, endpoint, maxRequests = 10, windowMs = 60000) {
  const key = `${clientIP}:${endpoint}`;
  const now = Date.now();

  if (!rateLimitStore.has(key)) {
    rateLimitStore.set(key, []);
  }

  const requests = rateLimitStore.get(key);
  const validRequests = requests.filter(timestamp => now - timestamp < windowMs);

  if (validRequests.length >= maxRequests) {
    throw new Error('Rate limit exceeded');
  }

  validRequests.push(now);
  rateLimitStore.set(key, validRequests);

  // Clean up old entries periodically
  if (Math.random() < 0.01) {
    for (const [k, v] of rateLimitStore.entries()) {
      const filtered = v.filter(timestamp => now - timestamp < windowMs);
      if (filtered.length === 0) {
        rateLimitStore.delete(k);
      } else {
        rateLimitStore.set(k, filtered);
      }
    }
  }
}

// Security: Generate access token for site access verification
async function generateAccessToken(secret) {
  if (!secret) {
    throw new Error('JWT_SECRET is not configured on the worker.');
  }

  const payload = {
    type: 'site_access',
    exp: Math.floor(Date.now() / 1000) + (2 * 60 * 60), // 2 hours validity
    iat: Math.floor(Date.now() / 1000),
    nonce: crypto.randomUUID()
  };

  const encoder = new TextEncoder();
  const header = { alg: 'HS256', typ: 'JWT' };

  const headerBase64 = btoa(JSON.stringify(header)).replace(/\+/g, '-').replace(/\//g, '_').replace(/=/g, '');
  const payloadBase64 = btoa(JSON.stringify(payload)).replace(/\+/g, '-').replace(/\//g, '_').replace(/=/g, '');

  const data = `${headerBase64}.${payloadBase64}`;
  const key = await crypto.subtle.importKey(
    'raw',
    encoder.encode(secret),
    { name: 'HMAC', hash: 'SHA-256' },
    false,
    ['sign']
  );

  const signature = await crypto.subtle.sign('HMAC', key, encoder.encode(data));
  const signatureBase64 = btoa(String.fromCharCode(...new Uint8Array(signature))).replace(/\+/g, '-').replace(/\//g, '_').replace(/=/g, '');

  return `${data}.${signatureBase64}`;
}

// JWT utility functions
async function generateJWT(userId, secret) {
  // Security fix: Force require secret key to prevent running in unsafe state
  if (!secret) {
    throw new Error('JWT_SECRET is not configured on the worker.');
  }

  const header = {
    alg: 'HS256',
    typ: 'JWT'
  };

  const payload = {
    userId: userId,
    exp: Math.floor(Date.now() / 1000) + (60 * 60), // 1 hours validity
    iat: Math.floor(Date.now() / 1000),
    nonce: crypto.randomUUID()
  };

  const encoder = new TextEncoder();
  // Base64URL encoding (no padding)
  const headerBase64 = btoa(JSON.stringify(header)).replace(/\+/g, '-').replace(/\//g, '_').replace(/=/g, '');
  const payloadBase64 = btoa(JSON.stringify(payload)).replace(/\+/g, '-').replace(/\//g, '_').replace(/=/g, '');

  const data = `${headerBase64}.${payloadBase64}`;
  const key = await crypto.subtle.importKey(
    'raw',
    encoder.encode(secret),
    { name: 'HMAC', hash: 'SHA-256' },
    false,
    ['sign']
  );

  const signature = await crypto.subtle.sign('HMAC', key, encoder.encode(data));
  const signatureBase64 = btoa(String.fromCharCode(...new Uint8Array(signature))).replace(/\+/g, '-').replace(/\//g, '_').replace(/=/g, '');

  return `${data}.${signatureBase64}`;
}

// Security: Verify access token for site access
async function verifyAccessToken(token, secret) {
  if (!secret) {
    throw new Error('JWT_SECRET is not configured on the worker.');
  }
  if (!token) {
    throw new Error('Access token required');
  }

  const [headerBase64, payloadBase64, signatureBase64] = token.split('.');

  if (!headerBase64 || !payloadBase64 || !signatureBase64) {
    throw new Error('Invalid token format');
  }

  const encoder = new TextEncoder();
  const data = `${headerBase64}.${payloadBase64}`;

  const key = await crypto.subtle.importKey(
    'raw',
    encoder.encode(secret),
    { name: 'HMAC', hash: 'SHA-256' },
    false,
    ['verify']
  );

  const signature = Uint8Array.from(atob(signatureBase64.replace(/-/g, '+').replace(/_/g, '/')), c => c.charCodeAt(0));
  const isValid = await crypto.subtle.verify('HMAC', key, signature, encoder.encode(data));

  if (!isValid) {
    throw new Error('Invalid access token signature');
  }

  const payload = JSON.parse(atob(payloadBase64.replace(/-/g, '+').replace(/_/g, '/')));

  if (payload.exp < Math.floor(Date.now() / 1000)) {
    throw new Error('Access token expired');
  }

  if (payload.type !== 'site_access') {
    throw new Error('Invalid access token type');
  }

  return payload;
}

async function verifyJWT(authHeader, secret) {
  // Security fix: Force require secret key
  if (!secret) {
    throw new Error('JWT_SECRET is not configured on the worker.');
  }
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    throw new Error('Invalid authorization header');
  }

  const token = authHeader.substring(7);
  const [headerBase64, payloadBase64, signatureBase64] = token.split('.');

  if (!headerBase64 || !payloadBase64 || !signatureBase64) {
    throw new Error('Invalid token format');
  }

  const encoder = new TextEncoder();
  const data = `${headerBase64}.${payloadBase64}`;

  const key = await crypto.subtle.importKey(
    'raw',
    encoder.encode(secret),
    { name: 'HMAC', hash: 'SHA-256' },
    false,
    ['verify']
  );

  // Logic fix: Correctly decode Base64URL format
  const signature = Uint8Array.from(atob(signatureBase64.replace(/-/g, '+').replace(/_/g, '/')), c => c.charCodeAt(0));
  const isValid = await crypto.subtle.verify('HMAC', key, signature, encoder.encode(data));

  if (!isValid) {
    throw new Error('Invalid signature');
  }

  const payload = JSON.parse(atob(payloadBase64.replace(/-/g, '+').replace(/_/g, '/')));

  if (payload.exp < Math.floor(Date.now() / 1000)) {
    throw new Error('Token expired');
  }

  return payload.userId;
}

// Security: Enhanced CORS handling function
function corsHeaders(origin, frontEnd_Domain) {
  // Security fix: Remove development origins in production
  const allowedOrigins = frontEnd_Domain.split(",");

  // Security: Only allow requests from whitelisted origins
  if (!origin || !allowedOrigins.includes(origin)) {
    throw new Error('Origin not allowed');
  }

  return {
    'Access-Control-Allow-Origin': origin,
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-Access-Token, X-Timestamp, X-Signature',
    'Access-Control-Max-Age': '86400',
    'Vary': 'Origin'
  };
}

function corsResponse(response, request, env) {
  const headers = new Headers(response.headers);
  // Logic fix: Get origin from request and pass to corsHeaders
  const origin = request.headers.get('Origin');

  try {
    if (!env.FRONTEND_DOMAIN) {
      console.error('FRONTEND_DOMAIN environment variable is not set');
      return new Response(JSON.stringify({ error: 'Server configuration error' }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      });
    }
    Object.entries(corsHeaders(origin, env.FRONTEND_DOMAIN)).forEach(([key, value]) => {
      headers.set(key, value);
    });
  } catch (error) {
    // If origin is not allowed, return error response
    return new Response(JSON.stringify({ error: 'Origin not allowed' }), {
      status: 403,
      headers: { 'Content-Type': 'application/json' }
    });
  }

  return new Response(response.body, {
    status: response.status,
    statusText: response.statusText,
    headers,
  });
}

// Security: Enhanced input validation functions
function validateEmail(email) {
  if (!email || typeof email !== 'string') return false;
  // More strict email validation
  const emailRegex = /^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/;
  return emailRegex.test(email) && email.length <= 254;
}

function validateNoteId(id) {
  if (!id || typeof id !== 'string') return false;
  return /^\d+$/.test(id) && parseInt(id) > 0 && parseInt(id) <= Number.MAX_SAFE_INTEGER;
}

function validateEncryptedContent(content) {
  if (!content || typeof content !== 'string') return false;
  // Check for reasonable length limits and base64-like format
  if (content.length === 0 || content.length > 1000000) return false; // 1MB limit
  const base64Pattern = /^[A-Za-z0-9+/=]+$/;
  if (!base64Pattern.test(content)) return false;

  return true; // Content passed all validation checks
}

function validateAuthKey(authKey) {
  if (!authKey || typeof authKey !== 'string') return false;
  // Auth key should be base64 encoded and reasonable length
  return authKey.length >= 32 && authKey.length <= 1024;
}

function validateSalt(salt) {
  if (!salt || !Array.isArray(salt)) return false;
  // Salt should be array of numbers (Uint8Array serialized)
  return salt.length === 16 && salt.every(n => typeof n === 'number' && n >= 0 && n <= 255);
}

// Security: Validate salt consistency between stored and provided values
function validateSaltConsistency(storedSalt, providedSalt) {
  if (!storedSalt || !providedSalt) return false;
  try {
    const stored = typeof storedSalt === 'string' ? JSON.parse(storedSalt) : storedSalt;
    const provided = Array.isArray(providedSalt) ? providedSalt : Array.from(providedSalt);

    if (!Array.isArray(stored) || !Array.isArray(provided)) return false;
    if (stored.length !== provided.length) return false;

    return stored.every((val, index) => val === provided[index]);
  } catch (error) {
    return false;
  }
}

// Security: Get client IP with proper header handling
function getClientIP(request) {
  // Check various headers for real IP (Cloudflare specific)
  return request.headers.get('CF-Connecting-IP') ||
         request.headers.get('X-Forwarded-For')?.split(',')[0]?.trim() ||
         request.headers.get('X-Real-IP') ||
         '127.0.0.1';
}

// Main Worker function
export default {
  async fetch(request, env) {
    // Handle CORS preflight requests
    if (request.method === 'OPTIONS') {
      try {
        if (!env.FRONTEND_DOMAIN) {
          console.error('FRONTEND_DOMAIN environment variable is not set');
          return new Response(JSON.stringify({ error: 'Server configuration error' }), {
            status: 500,
            headers: { 'Content-Type': 'application/json' }
          });
        }
        // Logic fix: Pass origin
        return new Response(null, { headers: corsHeaders(request.headers.get('Origin'), env.FRONTEND_DOMAIN) });
      } catch (error) {
        return new Response(JSON.stringify({ error: 'Origin not allowed' }), {
          status: 403,
          headers: { 'Content-Type': 'application/json' }
        });
      }
    }

    // Security: Rate limiting implementation
    const clientIP = getClientIP(request);
    const url = new URL(request.url);
    const path = url.pathname;

    try {
      // Apply rate limiting based on endpoint with configurable limits
      if (path === '/api/register') {
        checkRateLimit(clientIP, 'register', 
          parseInt(env.RATE_LIMIT_REGISTER_COUNT) || 5, 
          parseInt(env.RATE_LIMIT_REGISTER_WINDOW) || 3600000); // 5 requests per hour
      } else if (path === '/api/login') {
        checkRateLimit(clientIP, 'login', 
          parseInt(env.RATE_LIMIT_LOGIN_COUNT) || 5, 
          parseInt(env.RATE_LIMIT_LOGIN_WINDOW) || 60000); // 5 requests per minute
      } else if (path === '/api/verify-access') {
        checkRateLimit(clientIP, 'verify-access', 
          parseInt(env.RATE_LIMIT_VERIFY_COUNT) || 10, 
          parseInt(env.RATE_LIMIT_VERIFY_WINDOW) || 60000); // 10 requests per minute
      } else if (path === '/api/check-access-requirement') {
        checkRateLimit(clientIP, 'check-access-requirement', 
          parseInt(env.RATE_LIMIT_CHECK_COUNT) || 50, 
          parseInt(env.RATE_LIMIT_CHECK_WINDOW) || 60000); // 50 requests per minute
      } else if (path.startsWith('/api/notes')) {
        checkRateLimit(clientIP, 'notes',
          parseInt(env.RATE_LIMIT_NOTES_COUNT) || 100,
          parseInt(env.RATE_LIMIT_NOTES_WINDOW) || 60000); // 100 requests per minute
      } else if (path.startsWith('/api/admin')) {
        checkRateLimit(clientIP, 'admin',
          parseInt(env.RATE_LIMIT_ADMIN_COUNT) || 50,
          parseInt(env.RATE_LIMIT_ADMIN_WINDOW) || 60000); // 50 requests per minute for admin
      }

      // --- Site access verification endpoint (no authentication required) ---
      if (path === '/api/verify-access' && request.method === 'POST') {
        const startTime = Date.now();

        try {
          const body = await request.json();
          const { hash } = body;

          if (!hash || typeof hash !== 'string') {
            // Apply LOW_RISK timing protection for validation failures
            await applyTimingProtection(startTime, 'LOW_RISK');
            return corsResponse(new Response(JSON.stringify({
              success: false,
              error: 'Invalid request parameters'
            }), {
              status: 400,
              headers: { 'Content-Type': 'application/json' }
            }), request, env);
          }

          // Get access password from environment variables
          const accessPassword = env.SITE_ACCESS_PASSWORD;
          if (!accessPassword) {

            // Apply LOW_RISK timing protection for config errors
            await applyTimingProtection(startTime, 'LOW_RISK');
            return corsResponse(new Response(JSON.stringify({
              success: false,
              error: 'Server configuration error'
            }), {
              status: 500,
              headers: { 'Content-Type': 'application/json' }
            }), request, env);
          }

          // Calculate expected hash value
          const expectedHash = await sha256(accessPassword);

          // Verify hash
          const isValid = hash === expectedHash;

          // Apply MEDIUM_RISK timing protection for access verification
          await applyTimingProtection(startTime, 'MEDIUM_RISK');

          if (isValid) {
            // Generate access token for verified users
            if (!env.JWT_SECRET) {
              console.error('JWT_SECRET environment variable is not set');
              return corsResponse(new Response(JSON.stringify({
                success: false,
                error: 'Server configuration error'
              }), {
                status: 500,
                headers: { 'Content-Type': 'application/json' }
              }), request, env);
            }
            const accessToken = await generateAccessToken(env.JWT_SECRET);
            return corsResponse(new Response(JSON.stringify({
              success: true,
              accessToken: accessToken
            }), {
              headers: { 'Content-Type': 'application/json' }
            }), request, env);
          } else {
            return corsResponse(new Response(JSON.stringify({
              success: false,
              error: 'Invalid access password'
            }), {
              status: 401,
              headers: { 'Content-Type': 'application/json' }
            }), request, env);
          }
        } catch (error) {
          // Apply LOW_RISK timing protection for unexpected errors
          await applyTimingProtection(startTime, 'LOW_RISK');
          return corsResponse(new Response(JSON.stringify({
            success: false,
            error: 'Internal server error'
          }), {
            status: 500,
            headers: { 'Content-Type': 'application/json' }
          }), request, env);
        }
      }

      // Check site access requirement
      if (path === '/api/check-access-requirement' && request.method === 'GET') {
        const siteAccessRequired = env.SITE_ACCESS_REQUIRED === 'true';



        if (!siteAccessRequired) {
          // Auto-generate access token when site access is not required
          if (!env.JWT_SECRET) {
            console.error('JWT_SECRET environment variable is not set');
            return corsResponse(new Response(JSON.stringify({
              success: false,
              error: 'Server configuration error'
            }), {
              status: 500,
              headers: { 'Content-Type': 'application/json' }
            }), request, env);
          }
          const accessToken = await generateAccessToken(env.JWT_SECRET);



          return corsResponse(new Response(JSON.stringify({
            success: true,
            siteAccessRequired: false,
            accessToken: accessToken,
            message: 'Site access not required, auto-generated token'
          }), {
            headers: { 'Content-Type': 'application/json' }
          }), request, env);
        } else {

          return corsResponse(new Response(JSON.stringify({
            success: true,
            siteAccessRequired: true,
            message: 'Site access password required'
          }), {
            headers: { 'Content-Type': 'application/json' }
          }), request, env);
        }
      }

      // --- Public endpoints (no authentication required) ---
      if (path === '/api/salt' && request.method === 'GET') {
        const email = url.searchParams.get('email');
        if (!validateEmail(email)) {
          return corsResponse(new Response(JSON.stringify({ error: 'Invalid email format' }), {
            status: 400,
            headers: { 'Content-Type': 'application/json' }
          }), request, env);
        }

        // Generate fixed fake salt value (based on email, but always the same)
        const encoder = new TextEncoder();
        const emailBytes = encoder.encode(email + 'fake_salt_seed_constant');
        const hashBuffer = await crypto.subtle.digest('SHA-256', emailBytes);
        const hashArray = new Uint8Array(hashBuffer);
        const fakeSalt = Array.from(hashArray.slice(0, 16));

        // Always execute database query and same processing logic
        if (!env.DB) {
          console.error('DB environment variable is not set');
          return corsResponse(new Response(JSON.stringify({ error: 'Database configuration error' }), {
            status: 500,
            headers: { 'Content-Type': 'application/json' }
          }), request, env);
        }
        const user = await env.DB.prepare('SELECT salt FROM users WHERE email = ?').bind(email).first();
        
        // Always execute JSON parsing operations to maintain timing consistency
        let realSalt;
        let fakeJsonSalt = JSON.stringify(fakeSalt); // Create fake JSON string

        if (user && user.salt) {
          realSalt = JSON.parse(user.salt);
          // Execute fake JSON parsing to balance timing
          JSON.parse(fakeJsonSalt);
        } else {
          realSalt = fakeSalt;
          // Execute real JSON parsing to balance timing
          JSON.parse(fakeJsonSalt);
        }

        // No timing protection needed for salt endpoint (public, non-sensitive)

        return corsResponse(new Response(JSON.stringify({
          success: true,
          salt: realSalt
        }), {
          status: 200,
          headers: { 'Content-Type': 'application/json' }
        }), request, env);
      }

      // User registration - requires access token verification
      if (path === '/api/register' && request.method === 'POST') {
        const startTime = Date.now();

        try {
          // Security: Verify access token before allowing registration
          if (!env.JWT_SECRET) {
            console.error('JWT_SECRET environment variable is not set');
            return corsResponse(new Response(JSON.stringify({ error: 'Server configuration error' }), {
              status: 500,
              headers: { 'Content-Type': 'application/json' }
            }), request, env);
          }
          if (!env.DB) {
            console.error('DB environment variable is not set');
            return corsResponse(new Response(JSON.stringify({ error: 'Database configuration error' }), {
              status: 500,
              headers: { 'Content-Type': 'application/json' }
            }), request, env);
          }
          const accessToken = request.headers.get('X-Access-Token');
          try {
            await verifyAccessToken(accessToken, env.JWT_SECRET);
          } catch (error) {
            // Apply LOW_RISK timing protection for auth failures
            await applyTimingProtection(startTime, 'LOW_RISK');
            return corsResponse(new Response(JSON.stringify({ error: 'Access token required for registration' }), {
              status: 401,
              headers: { 'Content-Type': 'application/json' }
            }), request, env);
          }

          const { email, authKey, salt } = await request.json();
          if (!validateEmail(email) || !validateAuthKey(authKey) || !validateSalt(salt)) {
            // Apply LOW_RISK timing protection for validation failures
            await applyTimingProtection(startTime, 'LOW_RISK');
            return corsResponse(new Response(JSON.stringify({ error: 'Invalid input parameters' }), {
              status: 400,
              headers: { 'Content-Type': 'application/json' }
            }), request, env);
          }

          // Security: Use timing-safe database operations
          const result = await timingSafeUserRegistration(env.DB, email, authKey, salt);

          // Security: Apply HIGH_RISK timing protection for registration operations
          await applyTimingProtection(startTime, 'HIGH_RISK');

          // Always return the same response regardless of user existence
          return corsResponse(new Response(JSON.stringify({
            success: true,
            message: 'Registration request processed'
          }), {
            status: 200,
            headers: { 'Content-Type': 'application/json' }
          }), request, env);

        } catch (error) {
          // Security: Apply LOW_RISK timing protection for unexpected errors
          await applyTimingProtection(startTime, 'LOW_RISK');
          return corsResponse(new Response(JSON.stringify({
            error: 'Internal server error'
          }), {
            status: 500,
            headers: { 'Content-Type': 'application/json' }
          }), request, env);
        }
      }

      // User login - requires access token verification
      if (path === '/api/login' && request.method === 'POST') {
        const startTime = Date.now();

        try {
          // Security: Verify access token before allowing login
          if (!env.JWT_SECRET) {
            console.error('JWT_SECRET environment variable is not set');
            return corsResponse(new Response(JSON.stringify({ error: 'Server configuration error' }), {
              status: 500,
              headers: { 'Content-Type': 'application/json' }
            }), request, env);
          }
          if (!env.DB) {
            console.error('DB environment variable is not set');
            return corsResponse(new Response(JSON.stringify({ error: 'Database configuration error' }), {
              status: 500,
              headers: { 'Content-Type': 'application/json' }
            }), request, env);
          }
          const accessToken = request.headers.get('X-Access-Token');
          try {
            await verifyAccessToken(accessToken, env.JWT_SECRET);
          } catch (error) {
            // Apply LOW_RISK timing protection for auth failures
            await applyTimingProtection(startTime, 'LOW_RISK');
            return corsResponse(new Response(JSON.stringify({ error: 'Access token required for login' }), {
              status: 401,
              headers: { 'Content-Type': 'application/json' }
            }), request, env);
          }

          const { email, authKey } = await request.json();

          if (!validateEmail(email) || !validateAuthKey(authKey)) {
            // Apply LOW_RISK timing protection for validation failures
            await applyTimingProtection(startTime, 'LOW_RISK');
            return corsResponse(new Response(JSON.stringify({ error: 'Missing email or password' }), {
              status: 400,
              headers: { 'Content-Type': 'application/json' }
            }), request, env);
          }

          const user = await env.DB.prepare('SELECT id, salt FROM users WHERE is_disabled = 0 AND email = ? AND auth_key = ?').bind(email, authKey).first();

          // Apply MEDIUM_RISK timing protection for login operations
          await applyTimingProtection(startTime, 'MEDIUM_RISK');

          if (user) {
            // Security fix: Remove hardcoded fallback key
            const token = await generateJWT(user.id, env.JWT_SECRET);
            return corsResponse(new Response(JSON.stringify({
              success: true,
              token,
              salt: JSON.parse(user.salt)
            }), {
              headers: { 'Content-Type': 'application/json' }
            }), request, env);
          } else {
            return corsResponse(new Response(JSON.stringify({ error: 'Invalid username or password' }), {
              status: 401,
              headers: { 'Content-Type': 'application/json' }
            }), request, env);
          }
        } catch (error) {
          // Apply LOW_RISK timing protection for unexpected errors
          await applyTimingProtection(startTime, 'LOW_RISK');
          return corsResponse(new Response(JSON.stringify({
            error: 'Internal server error'
          }), {
            status: 500,
            headers: { 'Content-Type': 'application/json' }
          }), request, env);
        }
      }

      // --- Following endpoints require authentication ---
      // Security fix: Remove fallback key from all authentication calls
      if (!env.JWT_SECRET) {
        console.error('JWT_SECRET environment variable is not set');
        return corsResponse(new Response(JSON.stringify({ error: 'Server configuration error' }), {
          status: 500,
          headers: { 'Content-Type': 'application/json' }
        }), request, env);
      }
      if (!env.DB) {
        console.error('DB environment variable is not set');
        return corsResponse(new Response(JSON.stringify({ error: 'Database configuration error' }), {
          status: 500,
          headers: { 'Content-Type': 'application/json' }
        }), request, env);
      }
      const userId = await verifyJWT(request.headers.get('Authorization'), env.JWT_SECRET);

      // Check user status (whether disabled)
      await checkUserStatus(userId, env);

      // --- Admin-only endpoints ---

      // Check admin permissions
      if (path === '/api/admin/check' && request.method === 'GET') {
        try {
          console.log('Admin check request for user ID:', userId);

          // Check user status first
          await checkUserStatus(userId, env);

          const isAdmin = await isAdminUser(userId, env);
          console.log('Admin check result:', isAdmin);
          return corsResponse(new Response(JSON.stringify({
            success: true,
            isAdmin: isAdmin
          }), {
            headers: { 'Content-Type': 'application/json' }
          }), request, env);
        } catch (error) {
          console.error('Admin check error:', error);
          return corsResponse(new Response(JSON.stringify({
            success: false,
            error: 'Failed to check admin status'
          }), {
            status: 500,
            headers: { 'Content-Type': 'application/json' }
          }), request, env);
        }
      }

      // Get all users list (admin only)
      if (path === '/api/admin/users' && request.method === 'GET') {
        try {
          await verifyAdminPermission(request.headers.get('Authorization'), env);

          const users = await env.DB.prepare(`
            SELECT
              id,
              email,
              created_at,
              is_disabled,
              (SELECT COUNT(*) FROM notes WHERE user_id = users.id) as note_count
            FROM users
            ORDER BY created_at DESC
          `).all();

          return corsResponse(new Response(JSON.stringify({
            success: true,
            users: users.results || []
          }), {
            headers: { 'Content-Type': 'application/json' }
          }), request, env);

        } catch (error) {
          console.error('Admin users list error:', error.message);
          return corsResponse(new Response(JSON.stringify({
            success: false,
            error: 'Access denied'
          }), {
            status: 403,
            headers: { 'Content-Type': 'application/json' }
          }), request, env);
        }
      }

      // Disable/enable user (admin only)
      if (path.startsWith('/api/admin/users/') && path.endsWith('/toggle') && request.method === 'PUT') {
        try {
          const adminUserId = await verifyAdminPermission(request.headers.get('Authorization'), env);

          const targetUserId = path.split('/')[4]; // Extract user ID
          if (!targetUserId || isNaN(targetUserId)) {
            return corsResponse(new Response(JSON.stringify({
              success: false,
              error: 'Invalid user ID'
            }), {
              status: 400,
              headers: { 'Content-Type': 'application/json' }
            }), request, env);
          }

          // Prevent admin from disabling themselves
          if (parseInt(targetUserId) === adminUserId) {
            return corsResponse(new Response(JSON.stringify({
              success: false,
              error: 'Cannot disable your own account'
            }), {
              status: 400,
              headers: { 'Content-Type': 'application/json' }
            }), request, env);
          }

          const { disabled } = await request.json();

          const result = await env.DB.prepare('UPDATE users SET is_disabled = ? WHERE id = ?')
            .bind(disabled ? 1 : 0, targetUserId).run();

          if (result.meta.changes > 0) {
            return corsResponse(new Response(JSON.stringify({
              success: true,
              message: `User ${disabled ? 'disabled' : 'enabled'} successfully`
            }), {
              headers: { 'Content-Type': 'application/json' }
            }), request, env);
          } else {
            return corsResponse(new Response(JSON.stringify({
              success: false,
              error: 'User not found'
            }), {
              status: 404,
              headers: { 'Content-Type': 'application/json' }
            }), request, env);
          }

        } catch (error) {
          console.error('Admin toggle user error:', error.message);
          return corsResponse(new Response(JSON.stringify({
            success: false,
            error: 'Access denied'
          }), {
            status: 403,
            headers: { 'Content-Type': 'application/json' }
          }), request, env);
        }
      }

      // Admin backup status query
      if (path === '/api/admin/backup/status' && request.method === 'GET') {
        try {
          await verifyAdminPermission(request.headers.get('Authorization'), env);

          return corsResponse(new Response(JSON.stringify({
            success: true,
            backupEnabled: env.GOOGLE_DRIVE_ENABLED === 'true',
            retentionCount: parseInt(env.BACKUP_RETENTION_COUNT) || 30,
            lastBackupTime: 'Check Google Drive folder for recent backups'
          }), {
            headers: { 'Content-Type': 'application/json' }
          }), request, env);

        } catch (error) {
          console.error('Admin backup status error:', error.message);
          return corsResponse(new Response(JSON.stringify({
            success: false,
            error: 'Access denied'
          }), {
            status: 403,
            headers: { 'Content-Type': 'application/json' }
          }), request, env);
        }
      }

      // Admin manual backup
      if (path === '/api/admin/backup' && request.method === 'POST') {
        try {
          await verifyAdminPermission(request.headers.get('Authorization'), env);

          // Execute backup
          const result = await performDatabaseBackup(env);

          return corsResponse(new Response(JSON.stringify(result), {
            status: result.success ? 200 : 500,
            headers: { 'Content-Type': 'application/json' }
          }), request, env);

        } catch (error) {
          console.error('Admin manual backup error:', error.message);
          return corsResponse(new Response(JSON.stringify({
            success: false,
            error: 'Backup operation failed'
          }), {
            status: 500,
            headers: { 'Content-Type': 'application/json' }
          }), request, env);
        }
      }

      // Save note
      if (path === '/api/notes' && request.method === 'POST') {
        const { encryptedContent } = await request.json();

        if (!validateEncryptedContent(encryptedContent)) {
          return corsResponse(new Response(JSON.stringify({ error: 'Note content cannot be empty' }), {
            status: 400,
            headers: { 'Content-Type': 'application/json' }
          }), request, env);
        }

        const result = await env.DB.prepare('INSERT INTO notes (user_id, encrypted_content, created_at, updated_at) VALUES (?, ?, ?, ?)').bind(
          userId,
          encryptedContent,
          new Date().toISOString(),
          new Date().toISOString()
        ).run();

        return corsResponse(new Response(JSON.stringify({
          success: true,
          message: 'Note saved successfully',
          noteId: result.meta.last_row_id
        }), {
          status: 201,
          headers: { 'Content-Type': 'application/json' }
        }), request, env);
      }

      // Get notes list
      if (path === '/api/notes' && request.method === 'GET') {
        const { results } = await env.DB.prepare('SELECT id, encrypted_content, created_at, updated_at FROM notes WHERE user_id = ? ORDER BY updated_at DESC').bind(userId).all();
        return corsResponse(new Response(JSON.stringify({
          success: true,
          notes: results || []
        }), {
          headers: { 'Content-Type': 'application/json' }
        }), request, env);
      }

      // Update single note
      if (path.match(/^\/api\/notes\/\d+$/) && request.method === 'PUT') {
        const noteId = path.split('/').pop();

        if (!validateNoteId(noteId)) {
          return corsResponse(new Response(JSON.stringify({ error: 'Invalid note ID' }), {
            status: 400,
            headers: { 'Content-Type': 'application/json' }
          }), request, env);
        }

        const { encryptedContent } = await request.json();

        if (!validateEncryptedContent(encryptedContent)) {
          return corsResponse(new Response(JSON.stringify({ error: 'Note content cannot be empty' }), {
            status: 400,
            headers: { 'Content-Type': 'application/json' }
          }), request, env);
        }

        // Security fix: Combine verification and update in single atomic operation
        const result = await env.DB.prepare('UPDATE notes SET encrypted_content = ?, updated_at = ? WHERE id = ? AND user_id = ?').bind(
          encryptedContent,
          new Date().toISOString(),
          noteId,
          userId
        ).run();

        if (result.meta.changes > 0) {
          return corsResponse(new Response(JSON.stringify({
            success: true,
            message: 'Note updated successfully'
          }), {
            headers: { 'Content-Type': 'application/json' }
          }), request, env);
        } else {
          return corsResponse(new Response(JSON.stringify({ error: 'Note not found or access denied' }), {
            status: 404,  // More appropriate status code
            headers: { 'Content-Type': 'application/json' }
          }), request, env);
        }
      }

      // Delete single note
      if (path.match(/^\/api\/notes\/\d+$/) && request.method === 'DELETE') {
        const noteId = path.split('/').pop();

        if (!validateNoteId(noteId)) {
          return corsResponse(new Response(JSON.stringify({ error: 'Invalid note ID' }), {
            status: 400,
            headers: { 'Content-Type': 'application/json' }
          }), request, env);
        }

        // Security fix: Combine verification and delete in single atomic operation
        const result = await env.DB.prepare('DELETE FROM notes WHERE id = ? AND user_id = ?').bind(noteId, userId).run();

        if (result.meta.changes > 0) {
          return corsResponse(new Response(JSON.stringify({
            success: true,
            message: 'Note deleted successfully'
          }), {
            headers: { 'Content-Type': 'application/json' }
          }), request, env);
        } else {
          return corsResponse(new Response(JSON.stringify({ error: 'Note not found or access denied' }), {
            status: 404,  // More appropriate status code
            headers: { 'Content-Type': 'application/json' }
          }), request, env);
        }
      }

      // Delete all notes - requires explicit confirmation
      if (path === '/api/notes/clear' && request.method === 'DELETE') {
        const { confirmation } = await request.json();

        // Security fix: Require explicit confirmation for bulk delete
        if (confirmation !== 'DELETE_ALL_NOTES') {
          return corsResponse(new Response(JSON.stringify({
            error: 'Confirmation required. Send {"confirmation": "DELETE_ALL_NOTES"} to proceed.'
          }), {
            status: 400,
            headers: { 'Content-Type': 'application/json' }
          }), request, env);
        }

        await env.DB.prepare('DELETE FROM notes WHERE user_id = ?').bind(userId).run();
        return corsResponse(new Response(JSON.stringify({
          success: true,
          message: 'All notes deleted successfully'  // Don't reveal exact count
        }), {
          headers: { 'Content-Type': 'application/json' }
        }), request, env);
      }

      // Get user statistics
      if (path === '/api/stats' && request.method === 'GET') {
        const noteCount = await env.DB.prepare('SELECT COUNT(*) as count FROM notes WHERE user_id = ?').bind(userId).first();

        return corsResponse(new Response(JSON.stringify({
          success: true,
          stats: {
            noteCount: noteCount.count
            // Security fix: Remove sensitive user information (email, created_at)
          }
        }), {
          headers: { 'Content-Type': 'application/json' }
        }), request, env);
      }

      // Change password endpoint
      if (path === '/api/change-password' && request.method === 'POST') {
        try {
          const { currentAuthKey, newAuthKey, reencryptedNotes } = await request.json();

          // Validate input parameters
          if (!currentAuthKey || !newAuthKey || !Array.isArray(reencryptedNotes)) {
            return corsResponse(new Response(JSON.stringify({
              error: 'Missing required parameters'
            }), {
              status: 400,
              headers: { 'Content-Type': 'application/json' }
            }), request, env);
          }

          if (!validateAuthKey(currentAuthKey) || !validateAuthKey(newAuthKey)) {
            return corsResponse(new Response(JSON.stringify({
              error: 'Invalid authentication key format'
            }), {
              status: 400,
              headers: { 'Content-Type': 'application/json' }
            }), request, env);
          }

          // Security: Verify JWT token
          const authHeader = request.headers.get('Authorization');
          if (!authHeader || !authHeader.startsWith('Bearer ')) {
            return corsResponse(new Response(JSON.stringify({
              error: 'Authorization token required'
            }), {
              status: 401,
              headers: { 'Content-Type': 'application/json' }
            }), request, env);
          }

          if (!env.JWT_SECRET) {
            console.error('JWT_SECRET environment variable is not set');
            return corsResponse(new Response(JSON.stringify({
              error: 'Server configuration error'
            }), {
              status: 500,
              headers: { 'Content-Type': 'application/json' }
            }), request, env);
          }
          if (!env.DB) {
            console.error('DB environment variable is not set');
            return corsResponse(new Response(JSON.stringify({
              error: 'Database configuration error'
            }), {
              status: 500,
              headers: { 'Content-Type': 'application/json' }
            }), request, env);
          }
          let userId;
          try {
            userId = await verifyJWT(authHeader, env.JWT_SECRET);
          } catch (error) {
            return corsResponse(new Response(JSON.stringify({
              error: 'Invalid or expired token'
            }), {
              status: 401,
              headers: { 'Content-Type': 'application/json' }
            }), request, env);
          }

          // Get current user data
          const user = await env.DB.prepare('SELECT * FROM users WHERE id = ?').bind(userId).first();
          if (!user) {
            return corsResponse(new Response(JSON.stringify({
              error: 'User not found'
            }), {
              status: 404,
              headers: { 'Content-Type': 'application/json' }
            }), request, env);
          }

          // Verify current auth key
          if (currentAuthKey !== user.auth_key) {
            return corsResponse(new Response(JSON.stringify({
              error: 'Current authentication key is incorrect'
            }), {
              status: 401,
              headers: { 'Content-Type': 'application/json' }
            }), request, env);
          }

          // Start transaction to update auth key and re-encrypt notes
          // Note: We keep the same salt to maintain encryption key consistency
          const updateUserStmt = env.DB.prepare('UPDATE users SET auth_key = ? WHERE id = ?');
          const updateNoteStmt = env.DB.prepare('UPDATE notes SET encrypted_content = ? WHERE id = ? AND user_id = ?');

          // Update user auth key (keep same salt for encryption consistency)
          await updateUserStmt.bind(newAuthKey, userId).run();

          // Update all notes with re-encrypted content
          for (const note of reencryptedNotes) {
            // Convert note.id to string if it's a number
            const noteIdStr = String(note.id);

            if (!validateNoteId(noteIdStr) || !validateEncryptedContent(note.encrypted_content)) {
              throw new Error('Invalid note data format');
            }
            await updateNoteStmt.bind(note.encrypted_content, noteIdStr, userId).run();
          }

          return corsResponse(new Response(JSON.stringify({
            success: true,
            message: 'Password changed successfully'
          }), {
            headers: { 'Content-Type': 'application/json' }
          }), request, env);

        } catch (error) {
          return corsResponse(new Response(JSON.stringify({
            error: 'Failed to change password'
          }), {
            status: 500,
            headers: { 'Content-Type': 'application/json' }
          }), request, env);
        }
      }

      // All other unmatched paths should return 404
      return corsResponse(new Response(JSON.stringify({ error: 'API endpoint not found' }), {
        status: 404,
        headers: { 'Content-Type': 'application/json' }
      }), request, env);

    } catch (error) {
      // Security fix: Don't log sensitive error details
      // Return different status codes based on error type
      const isAuthError = error.message.includes('authorization') ||
                         error.message.includes('signature') ||
                         error.message.includes('Token') ||
                         error.message.includes('JWT_SECRET') ||
                         error.message.includes('Access token');

      const isRateLimitError = error.message.includes('Rate limit');

      let status = 500;
      let errorMessage = 'Internal server error';

      if (isRateLimitError) {
        status = 429;
        errorMessage = 'Too many requests, please try again later';
      } else if (isAuthError) {
        status = 401;
        errorMessage = 'Authentication required';  // More generic message
      }

      return corsResponse(new Response(JSON.stringify({
        error: errorMessage
      }), {
        status,
        headers: { 'Content-Type': 'application/json' }
      }), request, env);
    }
  },

  async scheduled(controller, env, ctx) {
    // Scheduled backup task
    console.log('Starting scheduled backup task...');

    try {
      const result = await performDatabaseBackup(env);

      if (result.success) {
        console.log('Scheduled backup completed successfully:', result);
      } else {
        console.error('Scheduled backup failed:', result.error);
      }
    } catch (error) {
      console.error('Error occurred during scheduled backup:', error);
    }
  }
};

/**
 * Timing-safe user registration function
 * Always performs both operations to maintain consistent timing
 */
async function timingSafeUserRegistration(db, email, authKey, salt) {
  const operationStartTime = Date.now();

  // Prepare both statements upfront
  const selectStmt = db.prepare('SELECT id FROM users WHERE email = ?');
  const insertStmt = db.prepare('INSERT INTO users (email, auth_key, salt, created_at) VALUES (?, ?, ?, ?)');
  const dummyStmt = db.prepare('SELECT COUNT(*) FROM users WHERE email = ? AND created_at > ?');

  try {
    // Always execute the SELECT operation first
    const existingUser = await selectStmt.bind(email).first();

    if (!existingUser) {
      // User doesn't exist, perform actual insert
      await insertStmt.bind(
        email,
        authKey,
        JSON.stringify(salt),
        new Date().toISOString()
      ).run();
    } else {
      // User exists, perform timing-equivalent dummy operation
      await dummyStmt.bind(
        email + '_dummy_' + Date.now(),
        new Date(Date.now() - 86400000).toISOString()
      ).first();
    }

    // Apply MEDIUM_RISK timing protection for database operations
    await applyTimingProtection(operationStartTime, 'MEDIUM_RISK');

    return { success: true, userExists: !!existingUser };

  } catch (error) {
    // Ensure timing protection even on database errors
    await applyTimingProtection(operationStartTime, 'LOW_RISK');
    throw error;
  }
}